import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';

export interface PaymentMethodField {
  key: string;
  label: string;
  type: 'amount' | 'text' | 'radio' | 'select';
  required?: boolean;
  min?: number;
  max?: number;
  placeholder?: string;
  options?: { label: string; value: any }[];
}

export interface PaymentMethod {
  value: string;
  label: string;
  icon: string;
  color: string;
  fields: PaymentMethodField[];
}

export interface PaymentData {
  selectedPaymentMethod: string;
  cashAmount?: number;
  upiAmount?: number;
  cardAmount?: number;
  upiId?: string;
  cardType?: 'credit' | 'debit';
  [key: string]: any;
}

export interface MultiPaymentData {
  cashAmount: number;
  onlineAmount: number;
  onlineMethod: 'upi' | 'card';
  upiId?: string;
  cardType?: 'credit' | 'debit';
}

export interface PaymentModalOutput {
  paymentMethod: string;
  paymentData: PaymentData;
  multiPaymentData?: MultiPaymentData;
  customerName: string;
  customerPhone: string;
}

@Injectable({ providedIn: 'root' })
export class PaymentService {
  private MAX_AMOUNT = 999999;
  private  paymentMethods: PaymentMethod[] = [
    {
      value: 'cash',
      label: 'Cash',
      icon: 'pi pi-money-bill',
      color: 'green',
      fields: [{ key: 'cashAmount', label: 'Amount Received', type: 'amount', required: true, min: 0 }]
    },
    {
      value: 'upi',
      label: 'UPI',
      icon: 'pi pi-wallet',
      color: 'blue',
      fields: [
        { key: 'upiAmount', label: 'Amount', type: 'amount', required: true, min: 1 },
        { key: 'upiId', label: 'UPI ID', type: 'text', placeholder: 'example@upi', required: true }
      ]
    },
    {
      value: 'card',
      label: 'Card',
      icon: 'pi pi-credit-card',
      color: 'indigo',
      fields: [
        { key: 'cardAmount', label: 'Amount', type: 'amount', required: true, min: 1 },
        {
          key: 'cardType',
          label: 'Card Type',
          type: 'radio',
          required: true,
          options: [{ label: 'Credit', value: 'credit' }, { label: 'Debit', value: 'debit' }]
        }
      ]
    },
  ];

  constructor(private messageService: MessageService) {}

  getPaymentMethods(totalAmount: number): PaymentMethod[] {
    return this.paymentMethods.map(method => ({
      ...method,
      fields: method.fields.map(field => ({
        ...field,
        max: field.type === 'amount' ? Math.max(totalAmount * 2, this.MAX_AMOUNT) : field.max
      }))
    }));
  }

  getPaymentMethod(methodValue: string, totalAmount: number): PaymentMethod | undefined {
    return this.getPaymentMethods(totalAmount).find(m => m.value === methodValue);
  }

  initializePaymentData(totalAmount: number): PaymentData {
    return {
      selectedPaymentMethod: 'cash',
      cashAmount: totalAmount
    };
  }

  validatePayment(paymentData: PaymentData, customerName: string, customerPhone: string, totalAmount: number, isSecondStep: boolean = false): boolean {
    // Validate customer information
    if (!customerName?.trim()) return this.showError('Customer name is required');
    if (!customerPhone?.trim()) return this.showError('Customer phone is required');
    if (customerPhone?.trim().length !== 10) {
      return this.showError('Phone number should be exact 10 digits');
    }


    // Validate payment method
    const method = this.getPaymentMethod(paymentData.selectedPaymentMethod, totalAmount);
    if (!method) return this.showError('Invalid payment method');

    // Validate required fields
    for (const field of method.fields) {
      if (field.required && !paymentData[field.key]?.toString().trim()) {
        return this.showError(`${field.label} is required`);
      }
    }

    // Validate payment amount
    const amountKey = `${paymentData.selectedPaymentMethod}Amount`;
    const amount = parseFloat(paymentData[amountKey] || 0);

    if (amount <= 0) return this.showError('Amount must be greater than 0');

    // For cash payments, allow partial payments
    if (paymentData.selectedPaymentMethod === 'cash' && !isSecondStep) {
      if (amount > totalAmount * 2) return this.showError('Amount exceeds reasonable limit');
      return true; // Allow any positive amount for cash
    }

    // For UPI/Card or second step payments, amount should match the required amount exactly
    if (amount < totalAmount) return this.showError(`Amount must be at least ₹${totalAmount.toFixed(2)}`);
    if (amount > totalAmount + 1) return this.showError(`Amount should not exceed ₹${totalAmount.toFixed(2)} by more than ₹1`);

    return true;
  }

  calculateAmounts(paymentData: PaymentData, totalAmount: number): { remaining: number; change: number } {
    const amountKey = `${paymentData.selectedPaymentMethod}Amount`;
    const paidAmount = parseFloat(paymentData[amountKey] || 0);
    return {
      remaining: Math.max(0, totalAmount - paidAmount),
      change: Math.max(0, paidAmount - totalAmount)
    };
  }



  private showError(message: string): boolean {
    this.messageService.add({ severity: 'error', summary: 'Error', detail: message });
    return false;
  }
}
