import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges } from '@angular/core';

import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput } from '../../services/payment.config.service';
import { ConfirmationService } from 'primeng/api';

@Component({
  selector: 'app-payment-modal',
  standalone: false,
  templateUrl: './payment-modal.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();

  paymentData: PaymentData = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  customerName = '';
  customerPhone = '';
  paymentMethods: PaymentMethod[] = [];

  // Multi-step payment properties
  cashPaymentCompleted = false;
  cashAmountPaid = 0;
  showOnlinePaymentStep = false;

  constructor(
    private paymentService: PaymentService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateAmounts();
      this.paymentMethods = this.paymentService.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }

  getSelectedMethod(): PaymentMethod | undefined {
    return this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);
  }

  private resetPaymentData(): void {
    this.paymentData = this.paymentService.initializePaymentData(this.totalAmount);
    this.customerName = '';
    this.customerPhone = '';
    this.cashPaymentCompleted = false;
    this.cashAmountPaid = 0;
    this.showOnlinePaymentStep = false;
    this.updateAmounts();
  }

  updateAmounts(): void {
    if (this.showOnlinePaymentStep) {
      // For second step, calculate based on remaining amount after cash payment
      const remainingAfterCash = this.totalAmount - this.cashAmountPaid;
      const amounts = this.paymentService.calculateAmounts(this.paymentData, remainingAfterCash);
      this.remainingAmount = amounts.remaining;
      this.change = amounts.change;
    } else {
      // For first step, calculate normally
      const amounts = this.paymentService.calculateAmounts(this.paymentData, this.totalAmount);
      this.remainingAmount = amounts.remaining;
      this.change = amounts.change;
    }
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;
      this.updateAmounts();
    }
  }

  onAmountInput(input: HTMLInputElement, fieldKey: string): void {
    this.paymentData[fieldKey] = parseFloat(input.value) || 0;
    this.updateAmounts();
  }

  onConfirm(): void {
    if (this.isProcessing) return;

    // If cash payment and there's remaining amount, proceed to online payment
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount > 0) {
      this.proceedToCashPayment();
      return;
    }

    // Validate and complete payment
    const validationAmount = this.getValidationAmount();
    const isSecondStep = this.showOnlinePaymentStep;
    if (this.paymentService.validatePayment(this.paymentData, this.customerName, this.customerPhone, validationAmount, isSecondStep)) {
      const output: PaymentModalOutput = {
        paymentMethod: this.getPaymentMethodForOutput(),
        paymentData: this.paymentData,
        customerName: this.customerName,
        customerPhone: this.customerPhone
      };

      // Add mixed payment data if applicable
      if (this.cashPaymentCompleted) {
        output.multiPaymentData = {
          cashAmount: this.cashAmountPaid,
          onlineAmount: this.getOnlineAmount(),
          onlineMethod: this.paymentData.selectedPaymentMethod as 'upi' | 'card',
          upiId: this.paymentData.selectedPaymentMethod === 'upi' ? this.paymentData.upiId : undefined,
          cardType: this.paymentData.selectedPaymentMethod === 'card' ? this.paymentData.cardType : undefined
        };
      }

      this.confirm.emit(output);
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }
  private proceedToCashPayment(): void {
    // Store cash payment details
    this.cashAmountPaid = this.paymentData.cashAmount || 0;
    this.cashPaymentCompleted = true;
    this.showOnlinePaymentStep = true;

    // Calculate remaining amount after cash payment
    const remainingAmount = this.totalAmount - this.cashAmountPaid;

    // Switch to UPI for remaining amount
    this.paymentData.selectedPaymentMethod = 'upi';
    this.paymentData.upiAmount = remainingAmount;

    // Clear cash amount to avoid confusion
    this.paymentData.cashAmount = 0;

    this.updateAmounts();
  }

  private getValidationAmount(): number {
    if (this.showOnlinePaymentStep) {
      // For second step, validate against the remaining amount after cash payment
      return this.totalAmount - this.cashAmountPaid;
    }
    return this.totalAmount;
  }

  private getPaymentMethodForOutput(): string {
    return this.cashPaymentCompleted ? 'mixed' : this.paymentData.selectedPaymentMethod;
  }

  private getOnlineAmount(): number {
    const amountKey = `${this.paymentData.selectedPaymentMethod}Amount`;
    return parseFloat(this.paymentData[amountKey] || '0');
  }

  onSwitchToUPI(): void {
    const remainingAmount = this.totalAmount - this.cashAmountPaid;
    this.paymentData.selectedPaymentMethod = 'upi';
    this.paymentData.upiAmount = remainingAmount;

    // Clear other payment amounts
    this.paymentData.cardAmount = 0;
    this.paymentData.cashAmount = 0;

    this.updateAmounts();
  }

  onSwitchToCard(): void {
    const remainingAmount = this.totalAmount - this.cashAmountPaid;
    this.paymentData.selectedPaymentMethod = 'card';
    this.paymentData.cardAmount = remainingAmount;

    // Clear other payment amounts
    this.paymentData.upiAmount = 0;
    this.paymentData.cashAmount = 0;

    this.updateAmounts();
  }

  cancelPartialPayment(): void {
    this.confirmationService.confirm({
      message: `Are you sure you want to cancel the partial payment? This will reset to cash payment of ₹${this.totalAmount.toFixed(2)}.`,
      header: 'Cancel Partial Payment',
      acceptButtonStyleClass: 'p-button-danger',
      accept: () => {
        this.resetToCashPayment();
      }
    });
  }

  private resetToCashPayment(): void {
    // Reset to cash payment with full amount
    this.cashPaymentCompleted = false;
    this.cashAmountPaid = 0;
    this.showOnlinePaymentStep = false;

    // Reset payment data to cash with full amount
    this.paymentData.selectedPaymentMethod = 'cash';
    this.paymentData.cashAmount = this.totalAmount;

    // Clear other payment amounts
    this.paymentData.upiAmount = 0;
    this.paymentData.cardAmount = 0;
    this.paymentData.upiId = '';
    this.paymentData.cardType = undefined;

    this.updateAmounts();
  }

  getConfirmButtonLabel(): string {
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount > 0 && !this.showOnlinePaymentStep) {
      return 'Proceed to Online Payment';
    }
    return this.confirmButtonLabel;
  }

  // Utility method for template
  parseFloat(value: string): number {
    return parseFloat(value);
  }
}